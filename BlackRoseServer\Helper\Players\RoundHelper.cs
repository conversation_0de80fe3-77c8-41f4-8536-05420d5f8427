﻿using BlackRoseServer.API;
using BlackRoseServer.Manager;
using HintServiceMeow.Core.Enum;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Utilities;
using HintServiceMeow.UI.Extension;
using LabApi.Features.Console;
using LabApi.Features.Wrappers;
using MEC;
using PlayerRoles.Spectating;
using PlayerRoles;
using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NorthwoodLib.Pools;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace BlackRoseServer.Helper.Players
{
    public sealed class RoundHelper
    {
        private static readonly Lazy<RoundHelper> _instance = new(() => new RoundHelper());
        private static readonly object _syncLock = new();
        
        public static RoundHelper Instance => _instance.Value;
        
        private RoundHelper()
        {
            _messageSlot = new ConcurrentDictionary<Player, Hint>();
            _playerStatsCache = new ConcurrentDictionary<string, PlayerStatsCache>();
            _performanceMetrics = new ConcurrentDictionary<string, long>();
            _playerDataAnalytics = new ConcurrentDictionary<string, PlayerAnalytics>();
            _eventHandlers = [];
            
            InitializeDefaultSettings();
        }
        
        private void InitializeDefaultSettings()
        {
            _settings = new RoundHelperSettings
            {
                UpdateInterval = 1.0f,
                CacheExpirySeconds = 5.0f,
                MaxTopPlayers = 3,
                EnableColoredRanks = true,
                EnablePerformanceTracking = true,
                EnableAnalytics = true
            };
        }

        public class RoundHelperSettings
        {
            public float UpdateInterval { get; set; }
            public float CacheExpirySeconds { get; set; }
            public int MaxTopPlayers { get; set; }
            public bool EnableColoredRanks { get; set; }
            public bool EnablePerformanceTracking { get; set; }
            public bool EnableAnalytics { get; set; }
        }
        
        private RoundHelperSettings _settings;
        
        public void Configure(Action<RoundHelperSettings> configAction)
        {
            lock (_syncLock)
            {
                configAction?.Invoke(_settings);
            }
        }
        
        public class PlayerStatsEventArgs : EventArgs
        {
            public Player Player { get; }
            public float Kills { get; }
            public float Deaths { get; }
            public float Damage { get; }
            public string KD { get; }
            public DateTime Timestamp { get; }
            
            public PlayerStatsEventArgs(Player player, float kills, float deaths, float damage, string kd)
            {
                Player = player;
                Kills = kills;
                Deaths = deaths;
                Damage = damage;
                KD = kd;
                Timestamp = DateTime.Now;
            }
        }
        
        private readonly List<Action<PlayerStatsEventArgs>> _eventHandlers;
        
        public void RegisterEventHandler(Action<PlayerStatsEventArgs> handler)
        {
            if (handler == null) return;
            
            lock (_syncLock)
            {
                _eventHandlers.Add(handler);
            }
        }
        
        public void UnregisterEventHandler(Action<PlayerStatsEventArgs> handler)
        {
            if (handler == null) return;
            
            lock (_syncLock)
            {
                _eventHandlers.Remove(handler);
            }
        }
        
        private void RaiseStatsUpdatedEvent(Player player, PlayerStatsCache stats)
        {
            if (player == null || stats == null) return;
            
            var args = new PlayerStatsEventArgs(player, stats.Kills, stats.Deaths, stats.Damage, stats.KD);
            
            foreach (var handler in _eventHandlers.ToArray())
            {
                    handler(args);
            }
        }

        private class PlayerAnalytics
        {
            public float HighestDamage { get; set; }
            public float AverageDamagePerRound { get; set; }
            public int RoundsPlayed { get; set; }
            public float KillDeathRatio { get; set; }
            public DateTime LastUpdated { get; set; }
            public List<float> DamageHistory { get; } = new List<float>();
            
            public void UpdateAnalytics(float kills, float deaths, float damage)
            {
                HighestDamage = Math.Max(HighestDamage, damage);
                DamageHistory.Add(damage);
                RoundsPlayed++;

                AverageDamagePerRound = DamageHistory.Sum() / DamageHistory.Count;

                KillDeathRatio = deaths > 0 ? kills / deaths : kills;
                
                LastUpdated = DateTime.Now;
            }
            
            public string GetAnalyticsReport()
            {
                StringBuilder report = StringBuilderPool.Shared.Rent();
                report.AppendLine($"最高伤害: {HighestDamage:F2}");
                report.AppendLine($"平均伤害: {AverageDamagePerRound:F2}");
                report.AppendLine($"回合数: {RoundsPlayed}");
                report.AppendLine($"KD比率: {KillDeathRatio:F2}");
                
                return StringBuilderPool.Shared.ToStringReturn(report);
            }
        }
        
        private readonly ConcurrentDictionary<string, PlayerAnalytics> _playerDataAnalytics;
        
        public string GetPlayerAnalytics(string userId)
        {
            if (string.IsNullOrEmpty(userId) || !_playerDataAnalytics.TryGetValue(userId, out var analytics))
            {
                return "没有可用的分析数据";
            }
            
            return analytics.GetAnalyticsReport();
        }
        
        private void UpdatePlayerAnalytics(Player player, PlayerStatsCache stats)
        {
            if (!_settings.EnableAnalytics || player == null || stats == null) return;
            
            _playerDataAnalytics.AddOrUpdate(
                player.UserId,
                _ => CreateNewAnalytics(stats),
                (_, existing) => UpdateExistingAnalytics(existing, stats)
            );
        }
        
        private PlayerAnalytics CreateNewAnalytics(PlayerStatsCache stats)
        {
            var analytics = new PlayerAnalytics();
            analytics.UpdateAnalytics(stats.Kills, stats.Deaths, stats.Damage);
            return analytics;
        }
        
        private PlayerAnalytics UpdateExistingAnalytics(PlayerAnalytics existing, PlayerStatsCache stats)
        {
            existing.UpdateAnalytics(stats.Kills, stats.Deaths, stats.Damage);
            return existing;
        }
        
        private readonly ConcurrentDictionary<string, long> _performanceMetrics;
        
        private void TrackPerformance(string operationName, Action operation)
        {
            var stopwatch = Stopwatch.StartNew();
            operation();
            stopwatch.Stop();
            
            _performanceMetrics.AddOrUpdate(
                operationName,
                stopwatch.ElapsedMilliseconds,
                (_, oldValue) => (oldValue * 9 + stopwatch.ElapsedMilliseconds) / 10
            );
        }
        
        public string GetPerformanceReport()
        {
            StringBuilder report = StringBuilderPool.Shared.Rent();
            report.AppendLine("PM:");
            
            foreach (var metric in _performanceMetrics.OrderByDescending(m => m.Value))
            {
                report.AppendLine($"{metric.Key}: {metric.Value}ms");
            }
            
            return StringBuilderPool.Shared.ToStringReturn(report);
        }
        
        private class PlayerStatsCache
        {
            public float Kills { get; set; }
            public float Deaths { get; set; }
            public float Damage { get; set; }
            public string KD { get; set; }
            public DateTime LastUpdated { get; set; }
            
            public bool IsStale => (DateTime.Now - LastUpdated).TotalSeconds > 5;
        }
        
        private readonly ConcurrentDictionary<string, PlayerStatsCache> _playerStatsCache;
        
        private PlayerStatsCache GetPlayerStats(Player player, Dictionary<string, float[]> playerData)
        {
            if (player == null) return null;
            
            var result = _playerStatsCache.AddOrUpdate(
                player.UserId,
                _ => CalculatePlayerStats(player, playerData),
                (_, cache) => (DateTime.Now - cache.LastUpdated).TotalSeconds > _settings.CacheExpirySeconds ? 
                    CalculatePlayerStats(player, playerData) : cache
            );

            UpdatePlayerAnalytics(player, result);
            RaiseStatsUpdatedEvent(player, result);
            
            return result;
        }
        
        private PlayerStatsCache CalculatePlayerStats(Player player, Dictionary<string, float[]> playerData)
        {
            if (player == null) return null;
            
            float kills = playerData.ContainsKey(player.UserId) ? playerData[player.UserId][0] : 0;
            float deaths = playerData.ContainsKey(player.UserId) ? playerData[player.UserId][1] : 0;
            float damage = playerData.ContainsKey(player.UserId) ? playerData[player.UserId][2] : 0;
            string kd = deaths != 0 ? (kills / deaths).ToString("F2") : kills.ToString();
            
            return new PlayerStatsCache
            {
                Kills = kills,
                Deaths = deaths,
                Damage = damage,
                KD = kd,
                LastUpdated = DateTime.Now
            };
        }
        
        private List<string> GetTopDamagePlayers(Dictionary<string, float[]> playerData, int? count = null)
        {
            int actualCount = count ?? _settings.MaxTopPlayers;

            return playerData
                .OrderByDescending(kvp => kvp.Value[2])
                .Take(actualCount)
                .Select(kvp => kvp.Key)
                .ToList();
        }

        private List<string> GetTopKillPlayers(Dictionary<string, float[]> playerData, int? count = null)
        {
            int actualCount = count ?? _settings.MaxTopPlayers;

            return playerData
                .OrderByDescending(kvp => kvp.Value[0])
                .Take(actualCount)
                .Select(kvp => kvp.Key)
                .ToList();
        }

        
        public IEnumerator<float> PlayerRoundInfo()
        {
            while (true)
            {
                yield return Timing.WaitForSeconds(_settings.UpdateInterval);

                if (Round.IsRoundEnded) yield break;

                if (_settings.EnablePerformanceTracking)
                {
                    TrackPerformance("RoundInfoUpdate", () => UpdateAllPlayerInfo());
                }
                else
                {
                    UpdateAllPlayerInfo();
                }
            }
        }
        
        private void UpdateAllPlayerInfo()
        {
                var playerData = Plugin.PlayerKillsDeaths;
                var topDamagePlayers = GetTopDamagePlayers(playerData);

                foreach (var messageSlot in _messageSlot)
                {
                    if (messageSlot.Key is null) continue;

                    var target = messageSlot.Key;
                    var playerStats = GetPlayerStats(target, playerData);

                    if (playerStats == null) continue;

                    StringBuilder list = StringBuilderPool.Shared.Rent();
                    TrackPerformance("BuildPlayerInfo", () => BuildPlayerInfoString(list, playerStats, target, topDamagePlayers, null, playerData));

                    string infoString = StringBuilderPool.Shared.ToStringReturn(list);
                    UpdatePlayerHintText(messageSlot.Value, infoString);
                }
        }
        
        private void BuildPlayerInfoString(StringBuilder list, PlayerStatsCache stats, Player target, List<string> topDamagePlayers, List<string> topKillPlayers, Dictionary<string, float[]> playerData)
        {
                AppendStyledHeader(list);

                // 当局得分：击杀*3 + 伤害/100
                double currentScore = 0;
                if (playerData.TryGetValue(target.UserId, out var data))
                {
                    currentScore = data[0] * 3 + data[2] / 100;
                }
                list.AppendLine($"当局得分:{currentScore:F1}");
                list.AppendLine($"Kills:{stats.Kills}");
                list.AppendLine($"伤害:{stats.Damage:F2}");



                list.AppendLine();

                // 只显示伤害榜，击杀榜移到回合结束时显示
                AppendDamageLeaderboard(list, topDamagePlayers, playerData);

                list.Append("</b>");
        }


        
        private void AppendStyledHeader(StringBuilder list)
        {
            list.Append("<b>");
        }
        
        private void AppendDamageLeaderboard(StringBuilder list, List<string> topPlayers, Dictionary<string, float[]> playerData)
        {
            list.AppendLine("------伤害榜");
            if (topPlayers.Any())
            {
                for (int i = 0; i < topPlayers.Count; i++)
                {
                    var userId = topPlayers[i];
                    var player = Player.Get(userId: userId);

                    if (player != null)
                    {
                        var damage = playerData[userId][2].ToString("F2");
                        var rank = i + 1;
                        var rankColor = GetRankColor(rank);
                        var displayName = TruncatePlayerName(player.Nickname, 12);

                        list.AppendLine($"<color={rankColor}>{rank}.</color>{displayName} {damage}");
                    }
                }
            }
        }

        private void AppendKillLeaderboard(StringBuilder list, List<string> topPlayers, Dictionary<string, float[]> playerData)
        {
            list.AppendLine("------击杀榜");
            if (topPlayers.Any())
            {
                for (int i = 0; i < topPlayers.Count; i++)
                {
                    var userId = topPlayers[i];
                    var player = Player.Get(userId: userId);

                    if (player != null)
                    {
                        var kills = playerData[userId][0].ToString("F0");
                        var rank = i + 1;
                        var rankColor = GetRankColor(rank);
                        var displayName = TruncatePlayerName(player.Nickname, 12);

                        list.AppendLine($"<color={rankColor}>{rank}.</color>{displayName} {kills}");
                    }
                }
            }
        }
        
        private void UpdatePlayerHintText(Hint hint, string infoString)
        {
            hint.Text = infoString;
        }

        private string GetRankColor(int rank)
        {
            if (!_settings.EnableColoredRanks)
                return "white";

            return rank switch
            {
                1 => "#FFD700",
                2 => "#C0C0C0",
                3 => "#CD7F32",
                _ => "white"
            };
        }

        /// <summary>
        /// 截断玩家名称，超过指定长度的部分用..替换
        /// </summary>
        /// <param name="playerName">玩家名称</param>
        /// <param name="maxLength">最大长度，默认为8</param>
        /// <returns>截断后的名称</returns>
        private string TruncatePlayerName(string playerName, int maxLength = 8)
        {
            if (string.IsNullOrEmpty(playerName))
                return "未知";

            if (playerName.Length <= maxLength)
                return playerName;

            return playerName.Substring(0, maxLength - 2) + "..";
        }
        
        public void InitForPlayer(Player player)
        {
            if (player == null)
            {
                return;
            }

            // 不再自动启动实时更新协程，只在回合结束时显示排行榜
            // EnsureCoroutineRunning();
            // RegisterPlayerHint(player);

            // 只初始化玩家数据缓存，不显示实时排行榜
            InitializePlayerCache(player);
        }
        
        /// <summary>
        /// 初始化玩家缓存（不启动实时显示）
        /// </summary>
        /// <param name="player">玩家对象</param>
        private void InitializePlayerCache(Player player)
        {
            if (player == null) return;

            // 初始化玩家统计缓存
            var playerData = Plugin.PlayerKillsDeaths;
            GetPlayerStats(player, playerData);

            Logger.Debug($"初始化玩家缓存 - 玩家: {player.Nickname}");
        }

        private void EnsureCoroutineRunning()
        {
            if (!_coroutine.IsRunning)
            {
                _coroutine = Timing.RunCoroutine(PlayerRoundInfo());
            }
        }
        
        private void RegisterPlayerHint(Player player)
        {
            var hint = CreatePlayerHint();
            _messageSlot[player] = hint;

                PlayerDisplay.Get(player.ReferenceHub).AddHint(hint);
        }
        
        private Hint CreatePlayerHint()
        {
            return new Hint
            {
                Alignment = HintAlignment.Right,
                YCoordinate = 300, // 伤害和击杀排行Y=300（减100）
                FontSize = 25,
                LineHeight = 5,
            };
        }
        
        public void RemovePlayer(Player player)
        {
            if (player == null) return;
            
            if (_messageSlot.TryRemove(player, out var hint))
            {
                PlayerDisplay.Get(player.ReferenceHub)?.RemoveHint(hint);
            }
            
            _playerStatsCache.TryRemove(player.UserId, out _);
        }
        
        public void Reset()
        {
            lock (_syncLock)
            {
                _messageSlot.Clear();
                _playerStatsCache.Clear();
                _performanceMetrics.Clear();
                _playerDataAnalytics.Clear();
                
                if (_coroutine.IsRunning)
                {
                    Timing.KillCoroutines(_coroutine);
                }
            }
        }
        
        public void OnRoundEnd()
        {
            try
            {
                Logger.Info("开始回合结束处理...");

                // 🚨 移除危险的延迟调用，直接显示排行榜
                // 延迟调用会在回合结束后访问Player.List，导致卡死
                try
                {
                    Logger.Info("立即显示回合结束排行榜...");
                    ShowFinalLeaderboard();
                }
                catch (Exception ex)
                {
                    Logger.Error($"显示回合结束排行榜失败: {ex.Message}");
                }

                // 立即清理资源
                _playerStatsCache.Clear();
                _messageSlot.Clear();

                if (_coroutine.IsRunning)
                {
                    Timing.KillCoroutines(_coroutine);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"回合结束处理失败: {ex.Message}");
                Logger.Debug($"OnRoundEnd详细错误: {ex}");
            }
        }

        private void ShowFinalLeaderboard()
        {
            try
            {
                var playerData = Plugin.PlayerKillsDeaths;
                Logger.Debug($"回合结束排行榜 - playerData是否为空: {playerData == null}, 数据数量: {playerData?.Count ?? 0}");

                if (playerData == null || playerData.Count == 0)
                {
                    Logger.Warn("回合结束排行榜 - 没有玩家数据，跳过显示");
                    return;
                }

                // 计算MVP（按当局得分计算）
                var mvpPlayer = CalculateMVP(playerData);
                Logger.Debug($"回合结束排行榜 - MVP玩家: {mvpPlayer ?? "无"}");

                // 🚨 避免访问Player.List，使用广播代替个人Hint
                // 在回合结束时Player.List可能不稳定，导致卡死
                Logger.Debug("回合结束排行榜 - 使用广播显示，避免Player.List访问");

                // 🚨 使用广播方式显示排行榜，完全避免Player.List访问
                StringBuilder commonBoard = StringBuilderPool.Shared.Rent();
                BuildCommonLeaderboard(commonBoard, playerData);
                string commonBoardText = StringBuilderPool.Shared.ToStringReturn(commonBoard);

                Logger.Debug($"回合结束排行榜 - 生成通用排行榜，长度: {commonBoardText.Length}");

                try
                {
                    // 🚨 使用HintManager显示排行榜，确保正确的位置和格式
                    // 广播可能位置不对，改用Hint系统
                    var config = new HintConfig
                    {
                        Type = HintType.Custom,
                        Text = commonBoardText,
                        YCoordinate = 300, // 得分排行Y=300，与伤害击杀排行统一（减100）
                        FontSize = 18, // 恢复原来的字体大小
                        Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                        Duration = 15f,
                        LifecycleType = HintLifecycleType.Temporary,
                        Priority = BlackRoseServer.Manager.HintPriority.High,
                        CustomId = "round_end_leaderboard_final",
                        AllowOverlap = false
                    };

                    // 使用安全的方式获取在线玩家，避免Player.List访问
                    var onlinePlayerIds = ExperienceSystemHelper.Instance.GetAllPlayerRoundExperience().Keys.ToList();
                    var successCount = 0;

                    foreach (var userId in onlinePlayerIds)
                    {
                        try
                        {
                            var player = Player.Get(userId: userId);
                            if (player != null && player.IsReady)
                            {
                                string hintId = HintManager.Instance.ShowHint(player, config);
                                if (!string.IsNullOrEmpty(hintId))
                                {
                                    successCount++;
                                }
                            }
                        }
                        catch (Exception playerEx)
                        {
                            Logger.Debug($"为玩家 {userId} 显示排行榜失败: {playerEx.Message}");
                        }
                    }

                    Logger.Info($"回合结束排行榜 - 已显示给 {successCount} 个玩家");
                }
                catch (Exception displayEx)
                {
                    Logger.Error($"回合结束排行榜显示失败: {displayEx.Message}");

                    // 降级方案：使用广播
                    try
                    {
                        XHelper.Broadcast(commonBoardText, 15, Broadcast.BroadcastFlags.Normal);
                        Logger.Info("回合结束排行榜 - 降级使用广播显示");
                    }
                    catch (Exception broadcastEx)
                    {
                        Logger.Error($"回合结束排行榜广播也失败: {broadcastEx.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"显示回合结束排行榜失败: {ex.Message}");
                Logger.Debug($"ShowFinalLeaderboard详细错误: {ex}");
            }
        }

        private string CalculateMVP(Dictionary<string, float[]> playerData)
        {
            if (playerData == null || playerData.Count == 0) return null;

            try
            {
                // 🚨 统一MVP计算逻辑：使用当局得分计算（击杀*3 + 伤害/100），与Plugin.cs中的逻辑保持一致
                var mvpByScore = playerData
                    .Where(kvp => kvp.Value[0] > 0 || kvp.Value[2] > 0) // 有击杀或伤害
                    .Select(kvp => new {
                        Key = kvp.Key,
                        Score = kvp.Value[0] * 3 + kvp.Value[2] / 100 // 当局得分
                    })
                    .OrderByDescending(x => x.Score)
                    .FirstOrDefault();

                if (mvpByScore != null && !string.IsNullOrEmpty(mvpByScore.Key))
                {
                    Logger.Debug($"MVP计算完成 - 玩家: {mvpByScore.Key}, 当局得分: {mvpByScore.Score:F2}");
                    return mvpByScore.Key;
                }

                Logger.Debug("没有找到有效的MVP（无得分数据）");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"计算MVP失败: {ex.Message}");
                return null;
            }
        }

        private void BuildCommonLeaderboard(StringBuilder list, Dictionary<string, float[]> playerData)
        {
            try
            {
                AppendStyledHeader(list);

                // 友伤信息（如果开启）
                if (Config.Instance?.EnableFriendlyFire == true)
                {
                    list.AppendLine("<align=center><color=#FF6B6B>⚠️ 友伤已开启 ⚠️</color></align>");
                    list.AppendLine();
                }



                // 使用表格布局：左侧伤害和击杀排行，右侧得分排行
                var topDamagePlayers = GetTopDamagePlayers(playerData, 4);
                var topKillPlayers = GetTopKillPlayers(playerData, 4);
                var topScorePlayers = GetTopScorePlayers(playerData, 8);

                // 构建三列布局
                BuildThreeColumnLeaderboard(list, topDamagePlayers, topKillPlayers, topScorePlayers, playerData);
            }
            catch (Exception ex)
            {
                Logger.Error($"构建通用排行榜失败: {ex.Message}");
                list.Clear();
                list.AppendLine("<color=#FF0000>排行榜生成失败</color>");
            }
        }

        private void BuildFinalLeaderboard(StringBuilder list, PlayerStatsCache stats, Player target, Dictionary<string, float[]> playerData, string mvpUserId)
        {
            AppendStyledHeader(list);

            // 友伤信息（如果开启）
            if (Config.Instance?.EnableFriendlyFire == true)
            {
                list.AppendLine("<align=center><color=#FF6B6B>⚠️ 友伤已开启 ⚠️</color></align>");
                list.AppendLine();
            }



            // 使用表格布局：左侧伤害和击杀排行，右侧得分排行
            var topDamagePlayers = GetTopDamagePlayers(playerData, 4);
            var topKillPlayers = GetTopKillPlayers(playerData, 4);
            var topScorePlayers = GetTopScorePlayers(playerData, 8);

            // 构建三列布局
            BuildThreeColumnLeaderboard(list, topDamagePlayers, topKillPlayers, topScorePlayers, playerData);
        }

        /// <summary>
        /// 构建三列排行榜布局（伤害榜在上，击杀榜在下）
        /// </summary>
        private void BuildThreeColumnLeaderboard(StringBuilder list, List<string> topDamage, List<string> topKills, List<string> topScore, Dictionary<string, float[]> playerData)
        {
            // 先显示伤害榜
            list.AppendLine("<align=left><size=28><color=#FF6B6B>🔥伤害榜</color></size></align>");

            int maxDamageRows = Math.Min(3, topDamage.Count);
            for (int i = 0; i < maxDamageRows; i++)
            {
                try
                {
                    var userId = topDamage[i];
                    var player = Player.Get(userId: userId);
                    var damage = playerData.ContainsKey(userId) ? playerData[userId][2] : 0f;
                    var name = TruncatePlayerName(player?.Nickname ?? "未知", 6);
                    list.AppendLine($"<align=left><size=24>{i + 1}.{name} {damage:F0}</size></align>");
                }
                catch (Exception ex)
                {
                    Logger.Debug($"处理伤害排行榜玩家时出错: {ex.Message}");
                    list.AppendLine($"<align=left><size=24>{i + 1}.未知 0</size></align>");
                }
            }

            // 再显示击杀榜（在伤害榜下方）
            list.AppendLine("<align=left><size=28><color=#4ECDC4>⚔️击杀榜</color></size></align>");

            int maxKillRows = Math.Min(3, topKills.Count);
            for (int i = 0; i < maxKillRows; i++)
            {
                try
                {
                    var userId = topKills[i];
                    var player = Player.Get(userId: userId);
                    var kills = playerData.ContainsKey(userId) ? (int)playerData[userId][0] : 0;
                    var name = TruncatePlayerName(player?.Nickname ?? "未知", 6);
                    list.AppendLine($"<align=left><size=24>{i + 1}.{name} {kills}杀</size></align>");
                }
                catch (Exception ex)
                {
                    Logger.Debug($"处理击杀排行榜玩家时出错: {ex.Message}");
                    list.AppendLine($"<align=left><size=24>{i + 1}.未知 0杀</size></align>");
                }
            }

            // 得分排行（右侧，显示当局得分：击杀*3 + 伤害/100）
            list.AppendLine("<align=right><size=28><color=#45B7D1>🏆 得分排行</color></size></align>");

            for (int i = 0; i < Math.Min(5, topScore.Count); i++)
            {
                try
                {
                    var userId = topScore[i];

                    // 从playerData中计算当局得分
                    if (!playerData.TryGetValue(userId, out var data))
                    {
                        Logger.Debug($"玩家: {userId} 没有战斗数据，跳过");
                        continue;
                    }

                    // 计算当局得分：击杀*3 + 伤害/100
                    double currentScore = data[0] * 3 + data[2] / 100;

                    if (currentScore <= 0)
                    {
                        Logger.Debug($"玩家: {userId} 当局得分为0，跳过");
                        continue;
                    }

                    // 尝试获取玩家名称，但如果失败就使用UserId
                    string name;
                    try
                    {
                        var player = Player.Get(userId: userId);
                        name = player != null ? TruncatePlayerName(player.Nickname ?? "未知", 7) : TruncatePlayerName(userId, 7);
                    }
                    catch
                    {
                        name = TruncatePlayerName(userId, 7);
                    }

                    list.AppendLine($"<align=right>{i + 1}. {name} - {currentScore:F1}分</align>");
                    Logger.Debug($"当局得分显示 - 玩家: {userId}, 得分: {currentScore:F1}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"处理得分排行榜玩家时出错: {ex.Message}");
                }
            }
        }



        private List<string> GetTopScorePlayers(Dictionary<string, float[]> playerData, int? count = null)
        {
            int actualCount = count ?? _settings.MaxTopPlayers;

            try
            {
                // 按当局得分排序：击杀*3 + 伤害/100
                return playerData
                    .Where(kvp => kvp.Value[0] > 0 || kvp.Value[2] > 0) // 有击杀或伤害
                    .Select(kvp => new {
                        Key = kvp.Key,
                        Score = kvp.Value[0] * 3 + kvp.Value[2] / 100 // 当局得分
                    })
                    .Where(x => x.Score > 0) // 只包含有得分的玩家
                    .OrderByDescending(x => x.Score)
                    .Take(actualCount)
                    .Select(x => x.Key)
                    .ToList();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取得分排行榜失败: {ex.Message}");
                // 降级方案：返回伤害排行榜的前几名
                return GetTopDamagePlayers(playerData, actualCount);
            }
        }

        private List<string> GetTopDamagePlayers(Dictionary<string, float[]> playerData, int count)
        {
            return playerData
                .OrderByDescending(kvp => kvp.Value[2]) // 按伤害排序
                .Take(count)
                .Select(kvp => kvp.Key)
                .ToList();
        }

        private List<string> GetTopKillPlayers(Dictionary<string, float[]> playerData, int count)
        {
            return playerData
                .OrderByDescending(kvp => kvp.Value[0]) // 按击杀排序
                .Take(count)
                .Select(kvp => kvp.Key)
                .ToList();
        }


        
        private CoroutineHandle _coroutine;
        private readonly ConcurrentDictionary<Player, Hint> _messageSlot;
    }
}
