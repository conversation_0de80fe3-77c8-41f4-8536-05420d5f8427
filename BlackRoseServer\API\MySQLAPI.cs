using LabApi.Features.Wrappers;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using LabApi.Features.Console;
using BlackRoseServer.Utils;

namespace BlackRoseServer.API
{
    /// <summary>
    /// MySQL数据库API扩展方法
    /// </summary>
    public static class MySQLAPI
    {
        /// <summary>
        /// 获取MySQL服务实例
        /// </summary>
        private static MySQLService Database => MySQLService.Instance;

        /// <summary>
        /// 检查数据库功能是否启用
        /// </summary>
        private static bool IsDatabaseEnabled()
        {
            return Database.IsDatabaseEnabled();
        }

        /// <summary>
        /// 获取玩家等级 (兼容旧API)
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>玩家等级</returns>
        public static int GetLVL(this Player player)
        {
            return GetLevel(player);
        }

        /// <summary>
        /// 获取玩家经验值 (兼容旧API)
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>玩家经验值</returns>
        public static int GetXP(this Player player)
        {
            return GetExperience(player);
        }

        /// <summary>
        /// 增加玩家经验值 (兼容旧API)
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="expValue">经验值增量</param>
        /// <param name="multiple">倍数</param>
        /// <returns>是否成功</returns>
        public static bool AddXP(this Player player, int expValue, double multiple = 1.0)
        {
            return AddExperience(player, expValue, multiple);
        }

        /// <summary>
        /// 更新玩家经验值 (兼容旧API)
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="expValue">经验值增量</param>
        /// <param name="multiple">倍数</param>
        /// <returns>是否成功</returns>
        public static bool UpdateXP(this Player player, int expValue, double multiple = 1.0)
        {
            return AddExperience(player, expValue, multiple);
        }

        /// <summary>
        /// 获取玩家显示UID（从players表）
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>BASE58编码的显示UID</returns>
        public static string GetDisplayUID(this Player player)
        {
            if (!IsDatabaseEnabled() || player == null)
                return "U1"; // 默认UID

            try
            {
                // 确保玩家记录存在
                EnsurePlayerExists(player);

                var sql = "SELECT DisplayUID FROM players WHERE UserId = @UserId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId)
                };

                var result = Database.ExecuteScalar(sql, parameters);
                if (result != null)
                {
                    var uid = Convert.ToInt32(result);
                    return Base58Encoder.GenerateUserID(uid);
                }

                return "U1"; // 如果没有找到，返回默认值
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家显示UID失败: {ex.Message}");
                return "U1";
            }
        }



        /// <summary>
        /// 获取玩家等级
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>玩家等级</returns>
        public static int GetLevel(this Player player)
        {
            if (!IsDatabaseEnabled() || player == null)
                return 1;

            try
            {
                var sql = "SELECT Level FROM players WHERE UserId = @UserId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId)
                };

                var result = Database.ExecuteScalar(sql, parameters);
                return result != null ? Convert.ToInt32(result) : 1;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家等级失败: {ex.Message}");
                return 1;
            }
        }

        /// <summary>
        /// 获取玩家经验值
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>玩家经验值</returns>
        public static int GetExperience(this Player player)
        {
            if (!IsDatabaseEnabled() || player == null)
                return 1;

            try
            {
                var sql = "SELECT Experience FROM players WHERE UserId = @UserId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId)
                };

                var result = Database.ExecuteScalar(sql, parameters);
                return result != null ? Convert.ToInt32(result) : 1;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家经验值失败: {ex.Message}");
                return 1;
            }
        }

        /// <summary>
        /// 设置玩家等级
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="level">等级</param>
        /// <returns>是否成功</returns>
        public static bool SetLevel(this Player player, int level)
        {
            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                // 确保玩家记录存在
                EnsurePlayerExists(player);

                var sql = "UPDATE players SET Level = @Level, LastUpdated = NOW() WHERE UserId = @UserId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@Level", level),
                    new MySqlParameter("@UserId", player.UserId)
                };

                var rowsAffected = Database.ExecuteNonQuery(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Logger.Error($"设置玩家等级失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置玩家经验值
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="experience">经验值</param>
        /// <returns>是否成功</returns>
        public static bool SetExperience(this Player player, int experience)
        {
            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                // 确保玩家记录存在
                EnsurePlayerExists(player);

                var sql = "UPDATE players SET Experience = @Experience, LastUpdated = NOW() WHERE UserId = @UserId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@Experience", experience),
                    new MySqlParameter("@UserId", player.UserId)
                };

                var rowsAffected = Database.ExecuteNonQuery(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Logger.Error($"设置玩家经验值失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 增加玩家经验值
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="expValue">经验值增量</param>
        /// <param name="multiple">倍数</param>
        /// <returns>是否成功</returns>
        public static bool AddExperience(this Player player, int expValue, double multiple = 1.0)
        {
            if (!IsDatabaseEnabled() || player == null || expValue <= 0)
                return false;

            if (player.DoNotTrack)
            {
                Logger.Debug($"玩家 {player.Nickname} 处于DNT状态，跳过AddExperience操作");
                return false;
            }

            try
            {
                // 计算实际经验值
                var actualExp = (int)(expValue * multiple);

                // 使用缓存系统
                PlayerDataCache.Instance.AddExperience(player.UserId, player.Nickname, actualExp);
                Logger.Debug($"AddExperience缓存成功 - 玩家: {player.Nickname}, 增加经验: {actualExp}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"增加玩家经验值失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 增加玩家经验（兼容旧API）
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="amount">经验值</param>
        public static void AddXP(this Player player, int amount)
        {
            player.AddExperience(amount, 1.0);
        }

        /// <summary>
        /// 设置玩家经验（兼容旧API）
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="amount">经验值</param>
        public static void SetXP(this Player player, int amount)
        {
            if (!IsDatabaseEnabled() || player == null)
                return;

            if (player.DoNotTrack)
            {
                Logger.Debug($"玩家 {player.Nickname} 处于DNT状态，跳过SetXP操作");
                return;
            }

            try
            {
                // 使用缓存系统
                PlayerDataCache.Instance.SetExperience(player.UserId, player.Nickname, amount);
                Logger.Debug($"SetXP缓存成功 - 玩家: {player.Nickname}, 设置经验: {amount}");
            }
            catch (Exception ex)
            {
                Logger.Error($"设置玩家经验失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取玩家进服信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>进服信息</returns>
        public static string GetJoinedInfo(this Player player)
        {
            if (!IsDatabaseEnabled() || player == null)
                return "";

            try
            {
                var sql = "SELECT Text FROM joinedinfo WHERE UserId = @UserId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId)
                };

                var result = Database.ExecuteScalar(sql, parameters);
                return result?.ToString() ?? "";
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家进服信息失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 设置玩家进服信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="text">进服信息</param>
        /// <returns>是否成功</returns>
        public static bool SetJoinedInfo(this Player player, string text)
        {
            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                var sql = @"
                    INSERT INTO joinedinfo (UserId, NickName, Text) 
                    VALUES (@UserId, @NickName, @Text)
                    ON DUPLICATE KEY UPDATE 
                    NickName = @NickName, Text = @Text";

                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId),
                    new MySqlParameter("@NickName", player.Nickname),
                    new MySqlParameter("@Text", text ?? "")
                };

                var rowsAffected = Database.ExecuteNonQuery(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Logger.Error($"设置玩家进服信息失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取玩家徽章信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>徽章信息</returns>
        public static (string text, string color) GetBadgeInfo(this Player player)
        {
            // 使用新的GetVisibleBadge方法，自动排除隐藏的称号
            return player.GetVisibleBadge();
        }

        /// <summary>
        /// 设置玩家徽章信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="text">徽章文本</param>
        /// <param name="color">徽章颜色</param>
        /// <param name="expireTime">过期时间</param>
        /// <returns>是否成功</returns>
        public static bool SetBadgeInfo(this Player player, string text, string color, DateTime expireTime)
        {
            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                var sql = @"
                    INSERT INTO badges (UserId, Text, Color, ExpireTime) 
                    VALUES (@UserId, @Text, @Color, @ExpireTime)
                    ON DUPLICATE KEY UPDATE 
                    Text = @Text, Color = @Color, ExpireTime = @ExpireTime";

                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId),
                    new MySqlParameter("@Text", text ?? ""),
                    new MySqlParameter("@Color", color ?? ""),
                    new MySqlParameter("@ExpireTime", expireTime)
                };

                var rowsAffected = Database.ExecuteNonQuery(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Logger.Error($"设置玩家徽章信息失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 确保玩家记录存在
        /// </summary>
        private static void EnsurePlayerExists(Player player)
        {
            try
            {
                var sql = @"
                    INSERT IGNORE INTO players (UserId, NickName, Level, Experience, IpAddress)
                    VALUES (@UserId, @NickName, 1, 1, @IpAddress)";

                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId),
                    new MySqlParameter("@NickName", player.Nickname ?? "Unknown"),
                    new MySqlParameter("@IpAddress", player.IpAddress ?? "0.0.0.0")
                };

                Database.ExecuteNonQuery(sql, parameters);
            }
            catch (Exception ex)
            {
                Logger.Error($"确保玩家记录存在失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 检查玩家是否存在，不存在则创建 (兼容旧API，同时创建players和t_user记录)
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>是否存在或创建成功</returns>
        public static bool CheckPlayer(this Player player)
        {
            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                // 检查玩家是否已存在于players表
                var checkSql = "SELECT COUNT(*) FROM players WHERE UserId = @UserId";
                var checkParameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId)
                };

                var exists = Convert.ToInt32(Database.ExecuteScalar(checkSql, checkParameters)) > 0;

                if (!exists)
                {
                    // 创建新玩家记录到players表
                    var insertPlayerSql = @"
                        INSERT INTO players (UserId, NickName, Level, Experience, IpAddress)
                        VALUES (@UserId, @NickName, 1, 1, @IpAddress)";

                    var insertPlayerParams = new MySqlParameter[]
                    {
                        new MySqlParameter("@UserId", player.UserId),
                        new MySqlParameter("@NickName", player.Nickname ?? "Unknown"),
                        new MySqlParameter("@IpAddress", player.IpAddress ?? "0.0.0.0")
                    };

                    Database.ExecuteNonQuery(insertPlayerSql, insertPlayerParams);
                    Logger.Debug($"新玩家 {player.Nickname} 已创建到players表");
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"检查/创建玩家失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 保存玩家数据 (兼容旧API)
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>是否成功</returns>
        public static bool SavePlayer(this Player player)
        {
            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                // 检查玩家是否已存在
                if (!CheckPlayer(player))
                {
                    // 新玩家，使用缓存系统标记
                    PlayerDataCache.Instance.MarkAsNewPlayer(player.UserId, player.Nickname);
                    Logger.Debug($"SavePlayer: 标记新玩家 - {player.Nickname}");
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"保存玩家数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置玩家昵称 (兼容旧API)
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="nickname">昵称</param>
        /// <returns>是否成功</returns>
        public static bool SetNickName(this Player player, string nickname)
        {
            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                // 确保玩家记录存在
                EnsurePlayerExists(player);

                var sql = "UPDATE players SET NickName = @NickName, LastUpdated = NOW() WHERE UserId = @UserId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@NickName", nickname ?? ""),
                    new MySqlParameter("@UserId", player.UserId)
                };

                var rowsAffected = Database.ExecuteNonQuery(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                Logger.Error($"设置玩家昵称失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 尝试获取玩家组信息 (兼容旧API)
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="group">输出的组信息</param>
        /// <returns>是否成功获取</returns>
        public static bool TryGetGroup(this Player player, out string group)
        {
            group = "";

            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                var sql = "SELECT PermissionName FROM players WHERE UserId = @UserId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId)
                };

                var result = Database.ExecuteScalar(sql, parameters);
                group = result?.ToString() ?? "";
                return !string.IsNullOrEmpty(group);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家组信息失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置玩家权限组
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="permissionName">权限组名称</param>
        /// <returns>是否成功</returns>
        public static bool SetPermissionName(this Player player, string permissionName)
        {
            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                // 确保玩家记录存在
                EnsurePlayerExists(player);

                if (string.IsNullOrEmpty(permissionName))
                {
                    // 删除权限记录
                    var deleteSql = "DELETE FROM permissions WHERE SteamId = @SteamId";
                    var deleteParams = new MySqlParameter[]
                    {
                        new MySqlParameter("@SteamId", player.UserId)
                    };
                    Database.ExecuteNonQuery(deleteSql, deleteParams);
                }
                else
                {
                    // 插入或更新权限记录
                    var upsertSql = @"
                        INSERT INTO permissions (SteamId, PermissionGroup, PlayerName, UpdatedAt)
                        VALUES (@SteamId, @PermissionGroup, @PlayerName, NOW())
                        ON DUPLICATE KEY UPDATE
                        PermissionGroup = @PermissionGroup,
                        PlayerName = @PlayerName,
                        UpdatedAt = NOW()";

                    var upsertParams = new MySqlParameter[]
                    {
                        new MySqlParameter("@SteamId", player.UserId),
                        new MySqlParameter("@PermissionGroup", permissionName),
                        new MySqlParameter("@PlayerName", player.Nickname ?? "")
                    };
                    Database.ExecuteNonQuery(upsertSql, upsertParams);
                }

                Logger.Info($"玩家 {player.Nickname} 权限组已设置为: {permissionName}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"设置玩家权限组失败 - 玩家: {player.Nickname}, 权限组: {permissionName}, 错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取玩家权限组
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>权限组名称</returns>
        public static string GetPermissionName(this Player player)
        {
            if (!IsDatabaseEnabled() || player == null)
            {
                Logger.Debug($"GetPermissionName: 数据库未启用或玩家为null - 玩家: {player?.Nickname ?? "null"}");
                return "";
            }

            try
            {
                Logger.Debug($"GetPermissionName: 查询玩家 {player.Nickname} 的权限组");

                var sql = "SELECT PermissionGroup FROM permissions WHERE SteamId = @SteamId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@SteamId", player.UserId)
                };

                var result = Database.ExecuteScalar(sql, parameters);
                string permissionName = result?.ToString() ?? "";

                Logger.Debug($"GetPermissionName: 玩家 {player.Nickname} 查询结果: '{permissionName}'");
                return permissionName;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家权限组失败 - 玩家: {player.Nickname}, 错误: {ex.Message}");
                Logger.Error($"SQL异常堆栈: {ex.StackTrace}");
                return "";
            }
        }

        /// <summary>
        /// 通过Steam ID设置权限（支持离线玩家）
        /// </summary>
        /// <param name="steamId">Steam ID</param>
        /// <param name="permissionGroup">权限组名称</param>
        /// <param name="qq">QQ号码（可选）</param>
        /// <param name="playerName">玩家名称（可选）</param>
        /// <returns>是否成功</returns>
        public static bool SetPermissionBySteamId(string steamId, string permissionGroup, string qq = null, string playerName = null)
        {
            if (!IsDatabaseEnabled() || string.IsNullOrEmpty(steamId))
                return false;

            try
            {
                if (string.IsNullOrEmpty(permissionGroup))
                {
                    // 删除权限记录
                    var deleteSql = "DELETE FROM permissions WHERE SteamId = @SteamId";
                    var deleteParams = new MySqlParameter[]
                    {
                        new MySqlParameter("@SteamId", steamId)
                    };
                    Database.ExecuteNonQuery(deleteSql, deleteParams);
                }
                else
                {
                    // 插入或更新权限记录
                    var upsertSql = @"
                        INSERT INTO permissions (SteamId, PermissionGroup, QQ, PlayerName, UpdatedAt)
                        VALUES (@SteamId, @PermissionGroup, @QQ, @PlayerName, NOW())
                        ON DUPLICATE KEY UPDATE
                        PermissionGroup = @PermissionGroup,
                        QQ = COALESCE(@QQ, QQ),
                        PlayerName = COALESCE(@PlayerName, PlayerName),
                        UpdatedAt = NOW()";

                    var upsertParams = new MySqlParameter[]
                    {
                        new MySqlParameter("@SteamId", steamId),
                        new MySqlParameter("@PermissionGroup", permissionGroup),
                        new MySqlParameter("@QQ", qq),
                        new MySqlParameter("@PlayerName", playerName)
                    };
                    Database.ExecuteNonQuery(upsertSql, upsertParams);
                }

                Logger.Info($"Steam ID {steamId} 权限组已设置为: {permissionGroup}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"设置Steam ID权限组失败 - Steam ID: {steamId}, 权限组: {permissionGroup}, 错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 通过QQ号码设置权限
        /// </summary>
        /// <param name="qq">QQ号码</param>
        /// <param name="permissionGroup">权限组名称</param>
        /// <param name="steamId">Steam ID（可选）</param>
        /// <param name="playerName">玩家名称（可选）</param>
        /// <returns>是否成功</returns>
        public static bool SetPermissionByQQ(string qq, string permissionGroup, string steamId = null, string playerName = null)
        {
            if (!IsDatabaseEnabled() || string.IsNullOrEmpty(qq))
                return false;

            try
            {
                if (string.IsNullOrEmpty(permissionGroup))
                {
                    // 删除权限记录
                    var deleteSql = "DELETE FROM permissions WHERE QQ = @QQ";
                    var deleteParams = new MySqlParameter[]
                    {
                        new MySqlParameter("@QQ", qq)
                    };
                    Database.ExecuteNonQuery(deleteSql, deleteParams);
                }
                else
                {
                    // 插入或更新权限记录
                    var upsertSql = @"
                        INSERT INTO permissions (SteamId, PermissionGroup, QQ, PlayerName, UpdatedAt)
                        VALUES (@SteamId, @PermissionGroup, @QQ, @PlayerName, NOW())
                        ON DUPLICATE KEY UPDATE
                        PermissionGroup = @PermissionGroup,
                        SteamId = COALESCE(@SteamId, SteamId),
                        PlayerName = COALESCE(@PlayerName, PlayerName),
                        UpdatedAt = NOW()";

                    var upsertParams = new MySqlParameter[]
                    {
                        new MySqlParameter("@SteamId", steamId ?? ""),
                        new MySqlParameter("@PermissionGroup", permissionGroup),
                        new MySqlParameter("@QQ", qq),
                        new MySqlParameter("@PlayerName", playerName)
                    };
                    Database.ExecuteNonQuery(upsertSql, upsertParams);
                }

                Logger.Info($"QQ {qq} 权限组已设置为: {permissionGroup}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"设置QQ权限组失败 - QQ: {qq}, 权限组: {permissionGroup}, 错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有权限记录
        /// </summary>
        /// <returns>权限记录列表</returns>
        public static List<PermissionData> GetAllPermissions()
        {
            var permissions = new List<PermissionData>();

            if (!IsDatabaseEnabled())
                return permissions;

            try
            {
                var sql = "SELECT Id, SteamId, QQ, PermissionGroup, PlayerName, CreatedAt, UpdatedAt FROM permissions ORDER BY UpdatedAt DESC";
                var dataTable = Database.ExecuteQuery(sql);

                if (dataTable?.Rows.Count > 0)
                {
                    foreach (System.Data.DataRow row in dataTable.Rows)
                    {
                        permissions.Add(new PermissionData
                        {
                            Id = Convert.ToInt32(row["Id"]),
                            SteamId = row["SteamId"]?.ToString() ?? "",
                            QQ = row["QQ"]?.ToString(),
                            PermissionGroup = row["PermissionGroup"]?.ToString() ?? "",
                            PlayerName = row["PlayerName"]?.ToString(),
                            CreatedAt = Convert.ToDateTime(row["CreatedAt"]),
                            UpdatedAt = Convert.ToDateTime(row["UpdatedAt"])
                        });
                    }
                }

                Logger.Debug($"获取到 {permissions.Count} 条权限记录");
                return permissions;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取权限记录失败: {ex.Message}");
                return permissions;
            }
        }

        /// <summary>
        /// 通过Steam ID查找权限记录
        /// </summary>
        /// <param name="steamId">Steam ID</param>
        /// <returns>权限记录，如果不存在则返回null</returns>
        public static PermissionData GetPermissionBySteamId(string steamId)
        {
            if (!IsDatabaseEnabled() || string.IsNullOrEmpty(steamId))
                return null;

            try
            {
                var sql = "SELECT Id, SteamId, QQ, PermissionGroup, PlayerName, CreatedAt, UpdatedAt FROM permissions WHERE SteamId = @SteamId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@SteamId", steamId)
                };

                var dataTable = Database.ExecuteQuery(sql, parameters);
                if (dataTable?.Rows.Count > 0)
                {
                    var row = dataTable.Rows[0];
                    return new PermissionData
                    {
                        Id = Convert.ToInt32(row["Id"]),
                        SteamId = row["SteamId"]?.ToString() ?? "",
                        QQ = row["QQ"]?.ToString(),
                        PermissionGroup = row["PermissionGroup"]?.ToString() ?? "",
                        PlayerName = row["PlayerName"]?.ToString(),
                        CreatedAt = Convert.ToDateTime(row["CreatedAt"]),
                        UpdatedAt = Convert.ToDateTime(row["UpdatedAt"])
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"通过Steam ID查找权限记录失败 - Steam ID: {steamId}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 通过QQ号码查找权限记录
        /// </summary>
        /// <param name="qq">QQ号码</param>
        /// <returns>权限记录，如果不存在则返回null</returns>
        public static PermissionData GetPermissionByQQ(string qq)
        {
            if (!IsDatabaseEnabled() || string.IsNullOrEmpty(qq))
                return null;

            try
            {
                var sql = "SELECT Id, SteamId, QQ, PermissionGroup, PlayerName, CreatedAt, UpdatedAt FROM permissions WHERE QQ = @QQ";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@QQ", qq)
                };

                var dataTable = Database.ExecuteQuery(sql, parameters);
                if (dataTable?.Rows.Count > 0)
                {
                    var row = dataTable.Rows[0];
                    return new PermissionData
                    {
                        Id = Convert.ToInt32(row["Id"]),
                        SteamId = row["SteamId"]?.ToString() ?? "",
                        QQ = row["QQ"]?.ToString(),
                        PermissionGroup = row["PermissionGroup"]?.ToString() ?? "",
                        PlayerName = row["PlayerName"]?.ToString(),
                        CreatedAt = Convert.ToDateTime(row["CreatedAt"]),
                        UpdatedAt = Convert.ToDateTime(row["UpdatedAt"])
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"通过QQ查找权限记录失败 - QQ: {qq}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 尝试获取玩家徽章信息 (兼容旧API)
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="badge">输出的徽章文本</param>
        /// <param name="color">输出的徽章颜色</param>
        /// <param name="time">输出的过期时间</param>
        /// <returns>是否成功获取</returns>
        public static bool TryGetBadge(this Player player, out string badge, out string color, out DateTime time)
        {
            badge = "";
            color = "";
            time = DateTime.MinValue;

            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                var sql = "SELECT Text, Color, ExpireTime FROM badges WHERE UserId = @UserId AND ExpireTime > NOW()";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId)
                };

                var dataTable = Database.ExecuteQuery(sql, parameters);
                if (dataTable.Rows.Count > 0)
                {
                    var row = dataTable.Rows[0];
                    badge = row["Text"]?.ToString() ?? "";
                    color = row["Color"]?.ToString() ?? "";
                    time = row["ExpireTime"] != DBNull.Value ? Convert.ToDateTime(row["ExpireTime"]) : DateTime.MinValue;
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家徽章信息失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 切换玩家称号显示状态
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>切换后的状态（true=隐藏，false=显示）</returns>
        public static bool ToggleBadgeVisibility(this Player player)
        {
            if (!IsDatabaseEnabled() || player == null)
                return false;

            try
            {
                // 确保玩家记录存在
                EnsurePlayerExists(player);

                // 确保IsHidden字段存在
                EnsureBadgeHiddenFieldExists();

                // 获取当前隐藏状态
                var getCurrentSql = "SELECT COALESCE(IsHidden, 0) FROM badges WHERE UserId = @UserId AND ExpireTime > NOW() LIMIT 1";
                var getCurrentParams = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId)
                };

                var currentResult = Database.ExecuteScalar(getCurrentSql, getCurrentParams);
                bool currentIsHidden = currentResult != null && Convert.ToBoolean(currentResult);

                // 切换状态
                bool newIsHidden = !currentIsHidden;

                // 更新所有有效称号的隐藏状态
                var updateSql = "UPDATE badges SET IsHidden = @IsHidden WHERE UserId = @UserId AND ExpireTime > NOW()";
                var updateParams = new MySqlParameter[]
                {
                    new MySqlParameter("@IsHidden", newIsHidden),
                    new MySqlParameter("@UserId", player.UserId)
                };

                Database.ExecuteNonQuery(updateSql, updateParams);

                Logger.Debug($"玩家 {player.Nickname} 称号显示状态已切换为: {(newIsHidden ? "隐藏" : "显示")}");
                return newIsHidden;
            }
            catch (Exception ex)
            {
                Logger.Error($"切换称号显示状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取玩家可见的称号（排除隐藏的）
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>可见的称号信息</returns>
        public static (string Text, string Color) GetVisibleBadge(this Player player)
        {
            if (!IsDatabaseEnabled() || player == null)
                return ("", "");

            try
            {
                // 确保IsHidden字段存在
                EnsureBadgeHiddenFieldExists();

                var sql = "SELECT Text, Color FROM badges WHERE UserId = @UserId AND ExpireTime > NOW() AND (IsHidden IS NULL OR IsHidden = 0) ORDER BY ExpireTime DESC LIMIT 1";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@UserId", player.UserId)
                };

                var dataTable = Database.ExecuteQuery(sql, parameters);
                if (dataTable?.Rows.Count > 0)
                {
                    var row = dataTable.Rows[0];
                    return (row["Text"]?.ToString() ?? "", row["Color"]?.ToString() ?? "");
                }

                return ("", "");
            }
            catch (Exception ex)
            {
                Logger.Error($"获取可见称号失败: {ex.Message}");
                return ("", "");
            }
        }

        /// <summary>
        /// 确保badges表中存在IsHidden字段
        /// </summary>
        private static void EnsureBadgeHiddenFieldExists()
        {
            try
            {
                // 检查字段是否已存在
                var checkSql = @"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'badges'
                    AND COLUMN_NAME = 'IsHidden'";

                var exists = Convert.ToInt32(Database.ExecuteScalar(checkSql, null)) > 0;

                if (!exists)
                {
                    // 添加IsHidden字段
                    var addFieldSql = @"
                        ALTER TABLE badges
                        ADD COLUMN IsHidden BOOLEAN DEFAULT FALSE COMMENT '是否隐藏称号'";

                    Database.ExecuteNonQuery(addFieldSql, null);
                    Logger.Info("badges表IsHidden字段已自动添加");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"确保IsHidden字段存在失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试权限系统功能
        /// </summary>
        public static void TestPermissionSystem()
        {
            try
            {
                Logger.Info("开始测试权限系统...");

                // 测试通过Steam ID设置权限
                var testSteamId = "76561198000000000";
                var testPermission = "admin";
                var testQQ = "123456789";
                var testPlayerName = "测试玩家";

                // 设置权限
                var setResult = SetPermissionBySteamId(testSteamId, testPermission, testQQ, testPlayerName);
                Logger.Info($"设置权限结果: {setResult}");

                // 查询权限
                var permission = GetPermissionBySteamId(testSteamId);
                if (permission != null)
                {
                    Logger.Info($"查询到权限: Steam ID={permission.SteamId}, 权限组={permission.PermissionGroup}, QQ={permission.QQ}, 玩家名={permission.PlayerName}");
                }
                else
                {
                    Logger.Debug("未查询到权限记录");
                }

                // 清理测试数据
                SetPermissionBySteamId(testSteamId, "");
                Logger.Info("权限系统测试完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"权限系统测试失败: {ex.Message}");
            }
        }
    }
}
