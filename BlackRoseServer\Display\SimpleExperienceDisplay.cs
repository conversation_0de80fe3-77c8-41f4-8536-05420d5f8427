using System;
using System.Collections.Concurrent;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Manager;
using BlackRoseServer.API;
using MEC;

namespace BlackRoseServer.Display
{
    /// <summary>
    /// 简化的经验显示系统 - 右下角显示"+X经验"
    /// </summary>
    public class SimpleExperienceDisplay : IDisposable
    {
        private static SimpleExperienceDisplay _instance;
        private static readonly object _lock = new object();

        private readonly ConcurrentDictionary<Player, string> _activeExperienceHints;
        private bool _disposed = false;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static SimpleExperienceDisplay Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new SimpleExperienceDisplay();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private SimpleExperienceDisplay()
        {
            _activeExperienceHints = new ConcurrentDictionary<Player, string>();
            Logger.Info("SimpleExperienceDisplay已初始化");
        }

        /// <summary>
        /// 显示经验获得提示
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="experienceGained">获得的经验值</param>
        /// <param name="isLevelUp">是否升级</param>
        public void ShowExperienceGain(Player player, int experienceGained, bool isLevelUp = false)
        {
            if (player == null || player.DoNotTrack || _disposed)
                return;

            try
            {
                // 移除之前的经验提示（如果存在）
                RemoveExperienceHint(player);

                string hintText;
                string hintId;

                if (isLevelUp)
                {
                    // 升级显示
                    var currentLevel = player.GetLVL();
                    hintText = $"<color=#FFD700><size=24><b>升级！</b></size></color>\n<color=#FFFF00>等级 {currentLevel}</color>\n<color=#00FF00>+{experienceGained} 经验</color>";
                    
                    var levelUpConfig = new HintConfig
                    {
                        Type = HintType.Custom,
                        Text = hintText,
                        YCoordinate = HintLayers.ExperienceGain, // 使用经验增加层级（Y=900，右对齐）
                        FontSize = 22,
                        Alignment = HintServiceMeow.Core.Enum.HintAlignment.Right,
                        Duration = 5f,
                        LifecycleType = HintLifecycleType.Temporary,
                        Priority = HintPriority.High,
                        CustomId = $"experience_levelup_{player.UserId}_{DateTime.Now.Ticks}",
                        AllowOverlap = true // 允许重叠显示
                    };

                    hintId = HintManager.Instance.ShowHint(player, levelUpConfig);
                }
                else
                {
                    // 普通经验获得显示
                    hintText = $"<color=#00FF00>+{experienceGained} 经验</color>";
                    
                    var expConfig = new HintConfig
                    {
                        Type = HintType.Custom,
                        Text = hintText,
                        YCoordinate = HintLayers.ExperienceGain, // 使用经验增加层级（Y=900，右对齐）
                        FontSize = 20,
                        Alignment = HintServiceMeow.Core.Enum.HintAlignment.Right,
                        Duration = 3f,
                        LifecycleType = HintLifecycleType.Temporary,
                        Priority = HintPriority.Normal,
                        CustomId = $"experience_gain_{player.UserId}_{DateTime.Now.Ticks}",
                        AllowOverlap = true // 允许重叠显示
                    };

                    hintId = HintManager.Instance.ShowHint(player, expConfig);
                }

                // 记录活跃的经验提示
                if (!string.IsNullOrEmpty(hintId))
                {
                    _activeExperienceHints.TryAdd(player, hintId);
                    
                    // 设置自动清理
                    float duration = isLevelUp ? 5f : 3f;
                    Timing.CallDelayed(duration, () =>
                    {
                        _activeExperienceHints.TryRemove(player, out _);
                    });
                }

                // Logger.Debug($"显示经验获得提示 - 玩家: {player.Nickname}, 经验: +{experienceGained}, 升级: {isLevelUp}");
            }
            catch (Exception ex)
            {
                Logger.Error($"显示经验获得提示失败: {ex.Message}");
                Logger.Debug($"ShowExperienceGain详细错误: {ex}");
            }
        }

        /// <summary>
        /// 显示经验获得提示（带自定义消息）
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="experienceGained">获得的经验值</param>
        /// <param name="message">自定义消息</param>
        /// <param name="color">颜色（可选）</param>
        public void ShowExperienceGainWithMessage(Player player, int experienceGained, string message, string color = "#00FF00")
        {
            if (player == null || player.DoNotTrack || _disposed)
                return;

            try
            {
                // 移除之前的经验提示（如果存在）
                RemoveExperienceHint(player);

                string hintText = $"<color={color}>+{experienceGained} 经验</color>\n<color=#FFFFFF><size=16>{message}</size></color>";
                
                var expConfig = new HintConfig
                {
                    Type = HintType.Custom,
                    Text = hintText,
                    YCoordinate = HintLayers.ExperienceGain, // 使用经验增加层级（Y=900，右对齐）
                    FontSize = 20,
                    Alignment = HintServiceMeow.Core.Enum.HintAlignment.Right,
                    Duration = 4f,
                    LifecycleType = HintLifecycleType.Temporary,
                    Priority = HintPriority.Normal,
                    CustomId = $"experience_message_{player.UserId}_{DateTime.Now.Ticks}",
                    AllowOverlap = true // 允许重叠显示
                };

                string hintId = HintManager.Instance.ShowHint(player, expConfig);

                // 记录活跃的经验提示
                if (!string.IsNullOrEmpty(hintId))
                {
                    _activeExperienceHints.TryAdd(player, hintId);
                    
                    // 设置自动清理
                    Timing.CallDelayed(4f, () =>
                    {
                        _activeExperienceHints.TryRemove(player, out _);
                    });
                }

                // Logger.Debug($"显示经验获得提示（带消息） - 玩家: {player.Nickname}, 经验: +{experienceGained}, 消息: {message}");
            }
            catch (Exception ex)
            {
                Logger.Error($"显示经验获得提示（带消息）失败: {ex.Message}");
                Logger.Debug($"ShowExperienceGainWithMessage详细错误: {ex}");
            }
        }

        /// <summary>
        /// 移除玩家的经验提示
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void RemoveExperienceHint(Player player)
        {
            if (player == null)
                return;

            try
            {
                if (_activeExperienceHints.TryRemove(player, out var hintId))
                {
                    HintManager.Instance.RemoveHint(hintId);
                    Logger.Debug($"移除经验提示 - 玩家: {player.Nickname}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"移除经验提示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除所有经验提示
        /// </summary>
        public void RemoveAllExperienceHints()
        {
            try
            {
                foreach (var kvp in _activeExperienceHints.ToArray())
                {
                    HintManager.Instance.RemoveHint(kvp.Value);
                }
                _activeExperienceHints.Clear();
                Logger.Debug("已移除所有经验提示");
            }
            catch (Exception ex)
            {
                Logger.Error($"移除所有经验提示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查玩家是否有活跃的经验提示
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>是否有活跃提示</returns>
        public bool HasActiveExperienceHint(Player player)
        {
            return player != null && _activeExperienceHints.ContainsKey(player);
        }

        /// <summary>
        /// 获取活跃经验提示数量
        /// </summary>
        /// <returns>活跃提示数量</returns>
        public int GetActiveHintCount()
        {
            return _activeExperienceHints.Count;
        }

        /// <summary>
        /// 清理断开连接玩家的数据
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void CleanupPlayerData(Player player)
        {
            if (player == null)
                return;

            try
            {
                RemoveExperienceHint(player);
                Logger.Debug($"清理玩家经验显示数据 - 玩家: {player.Nickname}");
            }
            catch (Exception ex)
            {
                Logger.Error($"清理玩家经验显示数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public string GetStatistics()
        {
            try
            {
                return $"SimpleExperienceDisplay统计:\n" +
                       $"- 活跃经验提示: {_activeExperienceHints.Count}";
            }
            catch (Exception ex)
            {
                Logger.Error($"获取经验显示统计信息失败: {ex.Message}");
                return "统计信息获取失败";
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            try
            {
                RemoveAllExperienceHints();
                _activeExperienceHints.Clear();
                Logger.Info("SimpleExperienceDisplay已释放");
            }
            catch (Exception ex)
            {
                Logger.Error($"释放SimpleExperienceDisplay失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 静态释放方法
        /// </summary>
        public static void DisposeInstance()
        {
            lock (_lock)
            {
                _instance?.Dispose();
                _instance = null;
            }
        }
    }
}
