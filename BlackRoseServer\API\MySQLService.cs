using System;
using System.Data;
using MySql.Data.MySqlClient;
using LabApi.Features.Console;

namespace BlackRoseServer.API
{
    /// <summary>
    /// MySQL数据库服务类
    /// </summary>
    public class MySQLService : IDisposable
    {
        private static MySQLService _instance;
        private static readonly object _lock = new object();
        private readonly string _connectionString;
        private MySqlConnection _connection;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static MySQLService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new MySQLService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private MySQLService()
        {
            try
            {
                _connectionString = GetConnectionString();
                
                if (string.IsNullOrEmpty(_connectionString))
                {
                    throw new InvalidOperationException("数据库连接字符串为空");
                }

                Logger.Debug($"MySQL连接字符串: {MaskConnectionString(_connectionString)}");

                // 测试连接
                TestConnection();

                // 初始化数据库结构
                InitializeDatabase();

                Logger.Info("MySQL数据库已初始化");
            }
            catch (Exception ex)
            {
                Logger.Error($"MySQL初始化失败: {ex.Message}");
                Logger.Debug($"MySQL初始化详细错误: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 获取连接字符串
        /// </summary>
        private string GetConnectionString()
        {
            try
            {
                var configConnectionString = Config.Instance?.DatabaseConnectionString;
                
                if (!string.IsNullOrEmpty(configConnectionString))
                {
                    return configConnectionString;
                }

                // 默认连接字符串
                return "server=localhost;database=blackrose;uid=root;pwd=;charset=utf8mb4;";
            }
            catch (Exception ex)
            {
                Logger.Error($"获取数据库连接字符串失败: {ex.Message}");
                return "server=localhost;database=blackrose;uid=root;pwd=;charset=utf8mb4;";
            }
        }

        /// <summary>
        /// 屏蔽连接字符串中的敏感信息
        /// </summary>
        private string MaskConnectionString(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return "";

            var password = GetPasswordFromConnectionString(connectionString);
            if (string.IsNullOrEmpty(password))
                return connectionString; // 如果没有密码，直接返回原字符串

            return connectionString.Replace(password, "***");
        }

        /// <summary>
        /// 从连接字符串中提取密码
        /// </summary>
        private string GetPasswordFromConnectionString(string connectionString)
        {
            try
            {
                if (string.IsNullOrEmpty(connectionString))
                    return "";

                var builder = new MySqlConnectionStringBuilder(connectionString);
                return builder.Password ?? "";
            }
            catch (Exception ex)
            {
                Logger.Debug($"解析连接字符串密码失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        private void TestConnection()
        {
            using (var connection = new MySqlConnection(_connectionString))
            {
                connection.Open();
                Logger.Debug("MySQL连接测试成功");
            }
        }

        /// <summary>
        /// 初始化数据库结构
        /// </summary>
        private void InitializeDatabase()
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    connection.Open();

                    Logger.Info("开始初始化数据库结构...");

                    // 检查数据库是否存在，如果不存在则创建
                    EnsureDatabaseExists(connection);

                    // 创建玩家数据表
                    CreatePlayersTable(connection);

                    // 创建进服信息表
                    CreateJoinedInfoTable(connection);

                    // 创建徽章信息表
                    CreateBadgesTable(connection);



                    // 创建权限管理表
                    CreatePermissionsTable(connection);

                    // 执行数据迁移
                    MigrateDataFromOldTables(connection);

                    // 创建存储过程
                    CreateStoredProcedures(connection);

                    // 验证表结构
                    ValidateTableStructure(connection);

                    Logger.Info("数据库表结构初始化完成");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"初始化数据库结构失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 确保数据库存在
        /// </summary>
        private void EnsureDatabaseExists(MySqlConnection connection)
        {
            try
            {
                var builder = new MySqlConnectionStringBuilder(_connectionString);
                var databaseName = builder.Database;

                if (string.IsNullOrEmpty(databaseName))
                {
                    Logger.Warn("连接字符串中未指定数据库名称");
                    return;
                }

                // 检查数据库是否存在
                var sql = $"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{databaseName}'";
                using (var command = new MySqlCommand(sql, connection))
                {
                    var result = command.ExecuteScalar();
                    if (result == null)
                    {
                        Logger.Info($"数据库 {databaseName} 不存在，正在创建...");

                        // 创建数据库
                        var createDbSql = $"CREATE DATABASE `{databaseName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                        using (var createCommand = new MySqlCommand(createDbSql, connection))
                        {
                            createCommand.ExecuteNonQuery();
                        }

                        Logger.Info($"数据库 {databaseName} 创建成功");
                    }
                    else
                    {
                        Logger.Debug($"数据库 {databaseName} 已存在");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"确保数据库存在失败: {ex.Message}");
                // 不抛出异常，因为数据库可能已经存在
            }
        }

        /// <summary>
        /// 验证表结构
        /// </summary>
        private void ValidateTableStructure(MySqlConnection connection)
        {
            try
            {
                var tables = new[] { "players", "joinedinfo", "badges", "permissions" };

                foreach (var table in tables)
                {
                    var sql = $"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = '{table}'";
                    using (var command = new MySqlCommand(sql, connection))
                    {
                        var exists = Convert.ToInt32(command.ExecuteScalar()) > 0;
                        if (exists)
                        {
                            Logger.Debug($"✅ 表 {table} 存在");
                        }
                        else
                        {
                            Logger.Error($"❌ 表 {table} 不存在");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"验证表结构失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建玩家数据表
        /// </summary>
        private void CreatePlayersTable(MySqlConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS players (
                    UserId VARCHAR(100) PRIMARY KEY,
                    DisplayUID INT AUTO_INCREMENT UNIQUE,
                    NickName VARCHAR(255) NOT NULL,
                    Level INT DEFAULT 1,
                    Experience INT DEFAULT 1,
                    IpAddress VARCHAR(45) DEFAULT '',
                    LastUpdated DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_nickname (NickName),
                    INDEX idx_level (Level),
                    INDEX idx_display_uid (DisplayUID)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

            using (var command = new MySqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// 创建进服信息表
        /// </summary>
        private void CreateJoinedInfoTable(MySqlConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS joinedinfo (
                    UserId VARCHAR(100) PRIMARY KEY,
                    NickName VARCHAR(255) NOT NULL,
                    Text TEXT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

            using (var command = new MySqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// 创建徽章信息表
        /// </summary>
        private void CreateBadgesTable(MySqlConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS badges (
                    UserId VARCHAR(100) PRIMARY KEY,
                    Text VARCHAR(255),
                    Color VARCHAR(50),
                    ExpireTime DATETIME,
                    INDEX idx_expire (ExpireTime)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

            using (var command = new MySqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }



        /// <summary>
        /// 创建权限管理表
        /// </summary>
        private void CreatePermissionsTable(MySqlConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS permissions (
                    Id INT AUTO_INCREMENT PRIMARY KEY,
                    SteamId VARCHAR(100) NOT NULL UNIQUE,
                    QQ VARCHAR(20) NULL COMMENT '人工管理字段',
                    PermissionGroup VARCHAR(100) NOT NULL,
                    PlayerName VARCHAR(255) NULL COMMENT '人工管理字段',
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_steamid (SteamId),
                    INDEX idx_permission_group (PermissionGroup)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

            using (var command = new MySqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// 从旧表迁移数据到新表结构
        /// </summary>
        private void MigrateDataFromOldTables(MySqlConnection connection)
        {
            try
            {
                Logger.Info("开始数据迁移...");

                // 迁移t_user表的DisplayUID到players表
                MigrateDisplayUIDFromTUser(connection);

                // 迁移players表的PermissionName到permissions表
                MigratePermissionsFromPlayers(connection);

                // 清理废弃的表
                CleanupDeprecatedTables(connection);

                Logger.Info("数据迁移完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"数据迁移失败: {ex.Message}");
                // 不抛出异常，允许系统继续运行
            }
        }

        /// <summary>
        /// 迁移t_user表的DisplayUID到players表
        /// </summary>
        private void MigrateDisplayUIDFromTUser(MySqlConnection connection)
        {
            try
            {
                // 检查t_user表是否存在
                var checkTableSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 't_user'";
                using (var checkCommand = new MySqlCommand(checkTableSql, connection))
                {
                    var tableExists = Convert.ToInt32(checkCommand.ExecuteScalar()) > 0;
                    if (!tableExists)
                    {
                        Logger.Debug("t_user表不存在，跳过DisplayUID迁移");
                        return;
                    }
                }

                // 检查players表是否已有DisplayUID字段
                var checkColumnSql = "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'players' AND column_name = 'DisplayUID'";
                using (var checkCommand = new MySqlCommand(checkColumnSql, connection))
                {
                    var columnExists = Convert.ToInt32(checkCommand.ExecuteScalar()) > 0;
                    if (!columnExists)
                    {
                        Logger.Debug("players表没有DisplayUID字段，跳过迁移");
                        return;
                    }
                }

                // 迁移DisplayUID数据
                var migrateSql = @"
                    UPDATE players p
                    INNER JOIN t_user u ON p.UserId = u.UserId
                    SET p.DisplayUID = u.Uid, p.IpAddress = COALESCE(u.IpAddress, '')
                    WHERE p.DisplayUID IS NULL OR p.DisplayUID = 0";

                using (var command = new MySqlCommand(migrateSql, connection))
                {
                    var rowsAffected = command.ExecuteNonQuery();
                    Logger.Info($"已迁移 {rowsAffected} 条DisplayUID记录");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"迁移DisplayUID失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 迁移players表的PermissionName到permissions表
        /// </summary>
        private void MigratePermissionsFromPlayers(MySqlConnection connection)
        {
            try
            {
                // 检查players表是否有PermissionName字段
                var checkColumnSql = "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'players' AND column_name = 'PermissionName'";
                using (var checkCommand = new MySqlCommand(checkColumnSql, connection))
                {
                    var columnExists = Convert.ToInt32(checkCommand.ExecuteScalar()) > 0;
                    if (!columnExists)
                    {
                        Logger.Debug("players表没有PermissionName字段，跳过权限迁移");
                        return;
                    }
                }

                // 迁移有权限的玩家到permissions表
                var migrateSql = @"
                    INSERT IGNORE INTO permissions (SteamId, PermissionGroup, PlayerName, CreatedAt, UpdatedAt)
                    SELECT UserId, PermissionName, NickName, NOW(), NOW()
                    FROM players
                    WHERE PermissionName IS NOT NULL AND PermissionName != ''";

                using (var command = new MySqlCommand(migrateSql, connection))
                {
                    var rowsAffected = command.ExecuteNonQuery();
                    Logger.Info($"已迁移 {rowsAffected} 条权限记录");
                }

                // 删除players表的PermissionName字段
                var dropColumnSql = "ALTER TABLE players DROP COLUMN IF EXISTS PermissionName";
                using (var command = new MySqlCommand(dropColumnSql, connection))
                {
                    command.ExecuteNonQuery();
                    Logger.Info("已删除players表的PermissionName字段");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"迁移权限数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理废弃的表
        /// </summary>
        private void CleanupDeprecatedTables(MySqlConnection connection)
        {
            try
            {
                var tablesToDrop = new[] { "t_user", "permission_records", "experience_records", "t_permission", "t_exp" };

                foreach (var table in tablesToDrop)
                {
                    try
                    {
                        var dropSql = $"DROP TABLE IF EXISTS {table}";
                        using (var command = new MySqlCommand(dropSql, connection))
                        {
                            command.ExecuteNonQuery();
                            Logger.Info($"已删除废弃表: {table}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Debug($"删除表 {table} 失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"清理废弃表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建存储过程
        /// </summary>
        private void CreateStoredProcedures(MySqlConnection connection)
        {
            try
            {
                // 删除旧的存储过程
                var dropOldProcedures = @"
                    DROP PROCEDURE IF EXISTS GetPermission;
                    DROP PROCEDURE IF EXISTS AddXP;
                    DROP PROCEDURE IF EXISTS GetLVL;
                    DROP PROCEDURE IF EXISTS GetXP;
                    DROP PROCEDURE IF EXISTS SetLVL;
                    DROP PROCEDURE IF EXISTS SetXP;
                    DROP PROCEDURE IF EXISTS SetNickName;
                    DROP PROCEDURE IF EXISTS GetBadge;";

                using (var command = new MySqlCommand(dropOldProcedures, connection))
                {
                    command.ExecuteNonQuery();
                }

                // 创建UpdateXP存储过程（升级逻辑）
                var updateXPProcedure = @"
                    DROP PROCEDURE IF EXISTS UpdateXP;
                    CREATE PROCEDURE UpdateXP(IN User_ID VARCHAR(30))
                    MODIFIES SQL DATA
                    BEGIN
                        DECLARE CurLVL INT DEFAULT 1;
                        DECLARE CurXP INT DEFAULT 1;
                        SET CurLVL=(SELECT Level FROM players WHERE UserId=User_ID);
                        SET CurXP=(SELECT Experience FROM players WHERE UserId=User_ID);
                        IF CurLVL<10 AND CurXP>=10 THEN
                            UPDATE players SET Level=Level+1 WHERE UserId=User_ID;
                            UPDATE players SET Experience=Experience-10 WHERE UserId=User_ID;
                        ELSEIF CurLVL<50 AND CurXP>=30 THEN
                            UPDATE players SET Level=Level+1 WHERE UserId=User_ID;
                            UPDATE players SET Experience=Experience-30 WHERE UserId=User_ID;
                        ELSEIF CurLVL<100 AND CurXP>=50 THEN
                            UPDATE players SET Level=Level+1 WHERE UserId=User_ID;
                            UPDATE players SET Experience=Experience-50 WHERE UserId=User_ID;
                        ELSEIF CurLVL<300 AND CurXP>=100 THEN
                            UPDATE players SET Level=Level+1 WHERE UserId=User_ID;
                            UPDATE players SET Experience=Experience-100 WHERE UserId=User_ID;
                        ELSEIF CurLVL<500 AND CurXP>=200 THEN
                            UPDATE players SET Level=Level+1 WHERE UserId=User_ID;
                            UPDATE players SET Experience=Experience-200 WHERE UserId=User_ID;
                        ELSEIF CurLVL<1000 AND CurXP>=500 THEN
                            UPDATE players SET Level=Level+1 WHERE UserId=User_ID;
                            UPDATE players SET Experience=Experience-500 WHERE UserId=User_ID;
                        ELSEIF CurXP>=1000 THEN
                            UPDATE players SET Level=Level+1 WHERE UserId=User_ID;
                            UPDATE players SET Experience=Experience-1000 WHERE UserId=User_ID;
                        END IF;
                    END;";

                using (var command = new MySqlCommand(updateXPProcedure, connection))
                {
                    command.ExecuteNonQuery();
                }

                Logger.Debug("存储过程创建完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"创建存储过程失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取数据库连接
        /// </summary>
        public MySqlConnection GetConnection()
        {
            return new MySqlConnection(_connectionString);
        }

        /// <summary>
        /// 检查数据库功能是否启用
        /// </summary>
        public bool IsDatabaseEnabled()
        {
            try
            {
                return Config.Instance?.EnableDatabaseFeatures == true && 
                       !string.IsNullOrEmpty(_connectionString);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 执行SQL查询
        /// </summary>
        public DataTable ExecuteQuery(string sql, params MySqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    using (var command = new MySqlCommand(sql, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        using (var adapter = new MySqlDataAdapter(command))
                        {
                            var dataTable = new DataTable();
                            adapter.Fill(dataTable);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"执行查询失败: {ex.Message}");
                Logger.Debug($"SQL: {sql}");
                throw;
            }
        }

        /// <summary>
        /// 执行SQL命令
        /// </summary>
        public int ExecuteNonQuery(string sql, params MySqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    using (var command = new MySqlCommand(sql, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        return command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"执行命令失败: {ex.Message}");
                Logger.Debug($"SQL: {sql}");
                throw;
            }
        }

        /// <summary>
        /// 执行标量查询
        /// </summary>
        public object ExecuteScalar(string sql, params MySqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    using (var command = new MySqlCommand(sql, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        return command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"执行标量查询失败: {ex.Message}");
                Logger.Debug($"SQL: {sql}");
                throw;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _connection?.Dispose();
        }
    }
}
