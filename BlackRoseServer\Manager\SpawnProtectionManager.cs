using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using MEC;
using HintServiceMeow.Core.Extension;
using UnityEngine;
using Logger = LabApi.Features.Console.Logger;

namespace BlackRoseServer.Manager
{
    /// <summary>
    /// 出生保护管理器
    /// </summary>
    public class SpawnProtectionManager
    {
        private static SpawnProtectionManager _instance;
        public static SpawnProtectionManager Instance => _instance ??= new SpawnProtectionManager();

        /// <summary>
        /// 受保护的玩家列表（UserId -> 保护结束时间）
        /// </summary>
        private readonly ConcurrentDictionary<string, DateTime> _protectedPlayers = new();

        /// <summary>
        /// 玩家出生位置记录（UserId -> 出生位置）
        /// </summary>
        private readonly ConcurrentDictionary<string, Vector3> _playerSpawnPositions = new();

        /// <summary>
        /// 协程句柄
        /// </summary>
        private CoroutineHandle _protectionCoroutine;

        /// <summary>
        /// 启动出生保护系统
        /// </summary>
        public void StartProtectionSystem()
        {
            if (Config.Instance?.EnableSpawnProtection != true)
            {
                Logger.Info("出生保护功能已禁用");
                return;
            }

            StopProtectionSystem();
            _protectionCoroutine = Timing.RunCoroutine(ProtectionCoroutine());
            Logger.Info($"出生保护系统已启动 - 保护时间: {Config.Instance.SpawnProtectionDuration}秒");
        }

        /// <summary>
        /// 停止出生保护系统
        /// </summary>
        public void StopProtectionSystem()
        {
            if (_protectionCoroutine.IsRunning)
            {
                Timing.KillCoroutines(_protectionCoroutine);
            }
            _protectedPlayers.Clear();
            _playerSpawnPositions.Clear();
            Logger.Debug("出生保护系统已停止");
        }

        /// <summary>
        /// 添加玩家到保护列表
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void AddProtection(Player player)
        {
            if (Config.Instance?.EnableSpawnProtection != true || player == null)
                return;

            var protectionEndTime = DateTime.Now.AddSeconds(Config.Instance.SpawnProtectionDuration);
            _protectedPlayers.AddOrUpdate(player.UserId, protectionEndTime, (key, oldValue) => protectionEndTime);

            // 记录玩家出生位置
            _playerSpawnPositions.AddOrUpdate(player.UserId, player.Position, (key, oldValue) => player.Position);

            Logger.Debug($"玩家 {player.Nickname} 已获得出生保护，持续 {Config.Instance.SpawnProtectionDuration} 秒，出生位置: {player.Position}");

            // 显示保护提示
            ShowProtectionHint(player);
        }

        /// <summary>
        /// 检查玩家是否受保护
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>是否受保护</returns>
        public bool IsProtected(Player player)
        {
            if (Config.Instance?.EnableSpawnProtection != true || player == null)
                return false;

            if (_protectedPlayers.TryGetValue(player.UserId, out DateTime endTime))
            {
                if (DateTime.Now < endTime)
                {
                    return true;
                }
                else
                {
                    // 保护时间已过，移除保护
                    _protectedPlayers.TryRemove(player.UserId, out _);
                }
            }

            return false;
        }

        /// <summary>
        /// 移除玩家保护
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="reason">移除原因</param>
        public void RemoveProtection(Player player, string reason = "未知原因")
        {
            if (player == null) return;

            var wasProtected = _protectedPlayers.TryRemove(player.UserId, out _);
            _playerSpawnPositions.TryRemove(player.UserId, out _);

            if (wasProtected)
            {
                Logger.Debug($"玩家 {player.Nickname} 的出生保护已移除 - 原因: {reason}");
            }
        }

        /// <summary>
        /// 显示保护提示
        /// </summary>
        /// <param name="player">玩家对象</param>
        private void ShowProtectionHint(Player player)
        {
            try
            {
                var protectionHint = new HintServiceMeow.Core.Models.Hints.Hint
                {
                    Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                    YCoordinate = 350,
                    FontSize = 18,
                    LineHeight = 4,
                    Text = $"<color=#00FF00>🛡️ 出生保护</color>\n<color=#FFFF00>保护时间: {Config.Instance.SpawnProtectionDuration}秒</color>\n<color=#CCCCCC>攻击他人或移动超过{Config.Instance.SpawnProtectionMoveThreshold}米将取消保护</color>"
                };

                player.AddHint(protectionHint);

                // 保护结束时移除提示
                Timing.CallDelayed(Config.Instance.SpawnProtectionDuration + 1f, () =>
                {
                    try
                    {
                        player.RemoveHint(protectionHint);
                    }
                    catch (Exception ex)
                    {
                        Logger.Debug($"移除保护提示失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"显示保护提示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保护系统协程
        /// </summary>
        /// <returns></returns>
        private IEnumerator<float> ProtectionCoroutine()
        {
            while (true)
            {
                yield return Timing.WaitForSeconds(1f);

                if (Round.IsRoundEnded)
                {
                    _protectedPlayers.Clear();
                    yield break;
                }

                // 检查保护状态和移动
                var playersToRemove = new List<string>();
                var now = DateTime.Now;

                foreach (var kvp in _protectedPlayers)
                {
                    var userId = kvp.Key;
                    var endTime = kvp.Value;
                    var player = Player.Get(userId);

                    if (player == null)
                    {
                        // 玩家已离线，移除保护
                        playersToRemove.Add(userId);
                        continue;
                    }

                    // 检查保护是否过期
                    if (now >= endTime)
                    {
                        playersToRemove.Add(userId);
                        Logger.Debug($"玩家 {player.Nickname} 的出生保护已过期");
                        continue;
                    }

                    // 检查玩家是否移动超过阈值
                    if (_playerSpawnPositions.TryGetValue(userId, out Vector3 spawnPos))
                    {
                        var currentPos = player.Position;
                        var distance = Vector3.Distance(spawnPos, currentPos);

                        if (distance > Config.Instance.SpawnProtectionMoveThreshold)
                        {
                            playersToRemove.Add(userId);
                            Logger.Debug($"玩家 {player.Nickname} 移动距离 {distance:F2}米 超过阈值 {Config.Instance.SpawnProtectionMoveThreshold}米，出生保护已取消");
                        }
                    }
                }

                // 移除需要清理的玩家
                foreach (var userId in playersToRemove)
                {
                    _protectedPlayers.TryRemove(userId, out _);
                    _playerSpawnPositions.TryRemove(userId, out _);
                }
            }
        }

        /// <summary>
        /// 获取保护状态统计
        /// </summary>
        /// <returns>保护状态信息</returns>
        public string GetProtectionStats()
        {
            var activeCount = 0;
            var now = DateTime.Now;

            foreach (var kvp in _protectedPlayers)
            {
                if (now < kvp.Value)
                {
                    activeCount++;
                }
            }

            return $"出生保护状态 - 活跃保护: {activeCount}, 总记录: {_protectedPlayers.Count}";
        }

        /// <summary>
        /// 清理所有保护
        /// </summary>
        public void ClearAllProtections()
        {
            var count = _protectedPlayers.Count;
            _protectedPlayers.Clear();
            _playerSpawnPositions.Clear();
            Logger.Info($"已清理 {count} 个出生保护记录");
        }
    }
}
